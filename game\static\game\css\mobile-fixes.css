/**
 * Mobile Fixes for Corporate Prompt Master Game
 * Comprehensive mobile responsiveness fixes
 */

/* Prevent horizontal scrolling on mobile and remove unnecessary scrollbars */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* Hide scrollbars but keep functionality */
@media (max-width: 992px) {
    /* Custom scrollbar styling for mobile */
    ::-webkit-scrollbar {
        width: 0px !important;
        background: transparent !important;
    }

    /* For Firefox */
    * {
        scrollbar-width: none !important;
    }
}

/* Fix viewport issues on mobile */
@viewport {
    width: device-width;
    zoom: 1.0;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 992px) {
    /* Fix app container layout */
    .app-container {
        flex-direction: column !important;
        height: 100vh !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
    }

    /* Hide sidebar by default on mobile */
    .sidebar {
        width: 100% !important;
        max-height: 0 !important;
        min-height: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
        border-right: none !important;
        border-bottom: none !important;
        position: relative !important;
        transform: none !important;
        left: auto !important;
        top: auto !important;
        padding: 0 !important;
        transition: all 0.3s ease !important;
    }

    /* Sidebar visible state when toggled */
    .sidebar-visible .sidebar {
        max-height: 250px !important;
        height: auto !important;
        padding: 1rem !important;
        overflow-y: auto !important;
        border-bottom: 1px solid var(--border-primary) !important;
    }

    /* Main content mobile layout - full height when sidebar hidden */
    .main-content {
        flex: 1 !important;
        height: 100vh !important;
        min-height: 0 !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        display: flex !important;
        flex-direction: column !important;
        margin-left: 0 !important;
        width: 100% !important;
    }

    /* Adjust main content when sidebar is visible */
    .sidebar-visible .main-content {
        height: calc(100vh - 250px) !important;
    }

    /* Header mobile optimizations */
    .header {
        flex-shrink: 0 !important;
        padding: 0.75rem !important;
        background: var(--bg-secondary) !important;
        border-bottom: 1px solid var(--border-primary) !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        position: relative !important;
        z-index: 1000 !important;
    }

    /* Ensure hamburger menu is visible */
    #sidebar-toggle {
        display: flex !important;
        order: -1 !important;
    }

    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
    }

    .mobile-sidebar-toggle:hover,
    .mobile-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .mobile-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Hide right sidebar toggle */
    .right-sidebar-toggle {
        display: none !important;
    }

    /* Messages container mobile */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding: 1rem !important;
        min-height: 0 !important;
        scrollbar-width: thin !important;
    }

    /* Hide scrollbar for messages container on mobile */
    .messages-container::-webkit-scrollbar {
        width: 3px !important;
        background: transparent !important;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3) !important;
        border-radius: 3px !important;
    }

    /* Input area mobile */
    .input-area {
        flex-shrink: 0 !important;
        padding: 1rem !important;
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-primary) !important;
    }

    /* Form controls mobile */
    .prompt-input, .response-editor {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
        padding: 0.75rem !important;
        border-radius: 8px !important;
        border: 2px solid var(--border-primary) !important;
        width: 100% !important;
        resize: vertical !important;
    }

    .prompt-input:focus, .response-editor:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        outline: none !important;
    }

    /* Buttons mobile */
    .btn, .preview-button, .primary-button, .secondary-button {
        min-height: 44px !important;
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        touch-action: manipulation !important;
    }

    /* Button groups mobile */
    .preview-actions, .edit-actions {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        margin-top: 1rem !important;
    }

    /* Messages mobile */
    .message {
        max-width: 95% !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .message-content {
        padding: 0.75rem 1rem !important;
        font-size: 0.95rem !important;
        line-height: 1.4 !important;
    }

    .message-sender {
        font-size: 0.8rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Character info mobile */
    .character-info {
        padding: 0.75rem !important;
    }

    .character-name {
        font-size: 1rem !important;
        font-weight: 600 !important;
    }

    .character-title {
        font-size: 0.85rem !important;
        opacity: 0.8 !important;
    }

    /* Hide instructions on mobile to save space */
    .instructions {
        display: none !important;
    }

    /* Modal mobile optimizations */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 12px !important;
    }

    .modal-body {
        padding: 1.5rem !important;
    }

    .modal-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .modal-buttons .btn {
        width: 100% !important;
    }
}

/* Small mobile devices (phones) */
@media (max-width: 576px) {
    .sidebar {
        max-height: 200px !important;
        padding: 0.5rem !important;
    }

    .main-content {
        height: calc(100vh - 200px) !important;
    }

    .header {
        padding: 0.5rem !important;
    }

    .messages-container {
        padding: 0.75rem !important;
    }

    .input-area {
        padding: 0.75rem !important;
    }

    .prompt-input, .response-editor {
        height: 70px !important;
        padding: 0.5rem !important;
    }

    .btn, .preview-button, .primary-button, .secondary-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .character-info {
        padding: 0.5rem !important;
    }

    .character-name {
        font-size: 0.9rem !important;
    }

    .character-title {
        font-size: 0.8rem !important;
    }

    .message-content {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* Landscape orientation fixes */
@media (max-width: 992px) and (orientation: landscape) {
    .sidebar {
        max-height: 150px !important;
    }

    .main-content {
        height: calc(100vh - 150px) !important;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    /* Larger touch targets */
    .btn, button, .mobile-sidebar-toggle {
        min-height: 48px !important;
        min-width: 48px !important;
    }

    /* Prevent text selection on UI elements */
    .mobile-sidebar-toggle, .btn, button {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Improve scrolling on touch devices */
    .messages-container, .sidebar {
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
    }
}

/* High DPI display fixes */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .app-container, .main-content, .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}
