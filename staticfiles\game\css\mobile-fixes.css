/**
 * Mobile Fixes for Corporate Prompt Master Game
 * Comprehensive mobile responsiveness fixes
 */

/* Prevent horizontal scrolling on mobile and remove unnecessary scrollbars */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove any body padding on mobile */
@media (max-width: 992px) {
    body {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
}

/* Hide scrollbars but keep functionality */
@media (max-width: 992px) {
    /* MOBILE SIDEBAR REDESIGN - COMPLETELY HIDDEN BY DEFAULT */
    .sidebar, .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: var(--bg-primary, #1a1a1a) !important;
        overflow-y: auto !important;
        padding: 2rem !important;
        border: none !important;
        box-shadow: none !important;
        transition: transform 0.3s ease-in-out !important;

        /* Hide by default - slide off screen */
        transform: translateX(-100%) !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* Right sidebar slides from right */
    .right-sidebar {
        transform: translateX(100%) !important;
    }

    /* Custom scrollbar styling for mobile */
    ::-webkit-scrollbar {
        width: 0px !important;
        background: transparent !important;
    }

    /* For Firefox */
    * {
        scrollbar-width: none !important;
    }
}

/* Fix viewport issues on mobile */
@viewport {
    width: device-width;
    zoom: 1.0;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 992px) {
    /* Remove padding-top from app container to eliminate white space */
    .app-container {
        flex-direction: column !important;
        height: 100vh !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        padding-top: 0 !important;
        margin-top: 0 !important;
    }

    /* Hide the fixed game header on mobile to remove white space */
    .game-header {
        display: none !important;
    }

    /* Hide hover header completely */
    .hover-header {
        display: none !important;
    }

    /* MOBILE SIDEBAR REDESIGN - FULL SCREEN OVERLAY WHEN EXPANDED */
    .sidebar, .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: var(--bg-primary, #1a1a1a) !important;
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease-in-out !important;
        overflow-y: auto !important;
        padding: 2rem !important;
        border: none !important;
        box-shadow: none !important;
        opacity: 1 !important;
    }

    /* Right sidebar slides from right */
    .right-sidebar {
        transform: translateX(100%) !important;
    }

    /* SIDEBAR VISIBLE STATES - SLIDE IN AND SHOW */
    .sidebar-visible .sidebar {
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .right-sidebar-visible .right-sidebar {
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Add overlay backdrop when sidebar is open */
    .sidebar-visible::before,
    .right-sidebar-visible::before {
        content: '' !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.5) !important;
        z-index: 9998 !important;
        backdrop-filter: blur(2px) !important;
    }

    /* MOBILE SIDEBAR CONTENT STYLING */
    .sidebar .logo h1,
    .right-sidebar .right-sidebar-title {
        font-size: 1.5rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        color: white !important;
    }

    .sidebar .character-info,
    .sidebar .game-stats,
    .sidebar .context-info,
    .sidebar .instructions,
    .right-sidebar .role-progression-container {
        margin-bottom: 1.5rem !important;
        padding: 1rem !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Make buttons more touch-friendly */
    .sidebar button,
    .right-sidebar button {
        min-height: 44px !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
        border-radius: 6px !important;
        margin: 0.5rem 0 !important;
    }

    /* Close button for mobile sidebars */
    .sidebar::before,
    .right-sidebar::before {
        content: '✕' !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        width: 44px !important;
        height: 44px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 1.2rem !important;
        cursor: pointer !important;
        z-index: 10000 !important;
    }

    /* Mobile sidebars hidden class */
    .mobile-sidebars-hidden .sidebar,
    .mobile-sidebars-hidden .right-sidebar {
        width: 0 !important;
        max-width: 0 !important;
        max-height: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        border: none !important;
    }

    /* Override any existing sidebar states on mobile */
    body .sidebar,
    body .right-sidebar {
        width: 0 !important;
        max-width: 0 !important;
        max-height: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        border: none !important;
    }

    /* Main content mobile layout - full screen when sidebars hidden */
    .main-content {
        flex: 1 !important;
        height: 100vh !important;
        min-height: 0 !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        display: flex !important;
        flex-direction: column !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        position: relative !important;
    }

    /* Adjust main content when sidebar is visible */
    .sidebar-visible .main-content {
        height: calc(100vh - 250px) !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Header mobile optimizations - make it the main header */
    .header {
        flex-shrink: 0 !important;
        padding: 0.75rem 1rem !important;
        background: #0f1117 !important;
        border-bottom: 1px solid var(--border-primary) !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        position: relative !important;
        z-index: 1000 !important;
        min-height: 56px !important;
    }

    /* Add title to mobile header */
    .header::after {
        content: "Corporate Prompt Master" !important;
        color: white !important;
        font-weight: bold !important;
        font-size: 1.1rem !important;
        position: absolute !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Ensure hamburger menu is visible */
    #sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
    }

    /* FORCE SHOW BOTH SIDEBAR TOGGLES ON MOBILE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        order: 1 !important;
        z-index: 1001 !important;
        margin-left: auto !important;
        width: 40px !important;
        height: 40px !important;
        cursor: pointer !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 6px !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background 0.2s ease !important;
    }

    .right-sidebar-toggle:hover,
    #right-sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Ensure hamburger menu is properly styled */
    #sidebar-toggle,
    .mobile-sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
        width: 40px !important;
        height: 40px !important;
        cursor: pointer !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 6px !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background 0.2s ease !important;
        margin-right: 1rem !important;
    }

    #sidebar-toggle:hover,
    .mobile-sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
    }

    .mobile-sidebar-toggle:hover,
    .mobile-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .mobile-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Hide right sidebar toggle */
    .right-sidebar-toggle {
        display: none !important;
    }

    /* Messages container mobile - fill remaining space */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding: 1rem !important;
        min-height: 0 !important;
        scrollbar-width: thin !important;
        height: calc(100vh - 56px - 80px) !important; /* viewport - header - input area */
        max-height: calc(100vh - 56px - 80px) !important;
    }

    /* Hide scrollbar for messages container on mobile */
    .messages-container::-webkit-scrollbar {
        width: 3px !important;
        background: transparent !important;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3) !important;
        border-radius: 3px !important;
    }

    /* Input area mobile - fixed at bottom */
    .input-area {
        flex-shrink: 0 !important;
        padding: 1rem !important;
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-primary) !important;
        min-height: 80px !important;
        max-height: 80px !important;
    }

    /* Form controls mobile */
    .prompt-input, .response-editor {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
        padding: 0.75rem !important;
        border-radius: 8px !important;
        border: 2px solid var(--border-primary) !important;
        width: 100% !important;
        resize: vertical !important;
    }

    .prompt-input:focus, .response-editor:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        outline: none !important;
    }

    /* Buttons mobile */
    .btn, .preview-button, .primary-button, .secondary-button {
        min-height: 44px !important;
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        touch-action: manipulation !important;
    }

    /* Button groups mobile */
    .preview-actions, .edit-actions {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        margin-top: 1rem !important;
    }

    /* Messages mobile */
    .message {
        max-width: 95% !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .message-content {
        padding: 0.75rem 1rem !important;
        font-size: 0.95rem !important;
        line-height: 1.4 !important;
    }

    .message-sender {
        font-size: 0.8rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Character info mobile */
    .character-info {
        padding: 0.75rem !important;
    }

    .character-name {
        font-size: 1rem !important;
        font-weight: 600 !important;
    }

    .character-title {
        font-size: 0.85rem !important;
        opacity: 0.8 !important;
    }

    /* Hide instructions on mobile to save space */
    .instructions {
        display: none !important;
    }

    /* Modal mobile optimizations */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 12px !important;
    }

    .modal-body {
        padding: 1.5rem !important;
    }

    .modal-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .modal-buttons .btn {
        width: 100% !important;
    }
}

/* Small mobile devices (phones) */
@media (max-width: 576px) {
    .sidebar {
        max-height: 200px !important;
        padding: 0.5rem !important;
    }

    .main-content {
        height: calc(100vh - 200px) !important;
    }

    .header {
        padding: 0.5rem !important;
    }

    .messages-container {
        padding: 0.75rem !important;
    }

    .input-area {
        padding: 0.75rem !important;
    }

    .prompt-input, .response-editor {
        height: 70px !important;
        padding: 0.5rem !important;
    }

    .btn, .preview-button, .primary-button, .secondary-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .character-info {
        padding: 0.5rem !important;
    }

    .character-name {
        font-size: 0.9rem !important;
    }

    .character-title {
        font-size: 0.8rem !important;
    }

    .message-content {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* Landscape orientation fixes */
@media (max-width: 992px) and (orientation: landscape) {
    .sidebar {
        max-height: 150px !important;
    }

    .main-content {
        height: calc(100vh - 150px) !important;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    /* Larger touch targets */
    .btn, button, .mobile-sidebar-toggle {
        min-height: 48px !important;
        min-width: 48px !important;
    }

    /* Prevent text selection on UI elements */
    .mobile-sidebar-toggle, .btn, button {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Improve scrolling on touch devices */
    .messages-container, .sidebar {
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
    }
}

/* High DPI display fixes */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .app-container, .main-content, .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}
